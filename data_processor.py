import pandas as pd
import json

# Set pandas display options to show all rows and columns
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

def process_company_data(excel_path, json_path):
    """
    Excel 파일을 읽어 JSON 파일로 변환하고, 업종별 회사 수를 분석합니다.

    Args:
        excel_path (str): 입력 Excel 파일 경로
        json_path (str): 출력 JSON 파일 경로
    """
    # Excel 파일 읽기
    try:
        df = pd.read_excel(excel_path)
    except FileNotFoundError:
        print(f"오류: '{excel_path}' 파일을 찾을 수 없습니다.")
        return

    # 데이터 가공
    company_list = []
    for index, row in df.iterrows():
        company_data = {
            'name': row['회사명'],
            'ticker': str(row['종목코드']).zfill(6) + '.KS',
            'industry': row['업종'],
            'main_product': row['주요제품']
        }
        company_list.append(company_data)

    # JSON 파일로 저장
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(company_list, f, ensure_ascii=False, indent=4)
    
    print(f"'{json_path}' 파일이 성공적으로 생성되었습니다.")

    # 업종 분석
    industry_counts = df['업종'].value_counts().sort_values(ascending=False)
    total_companies = len(df)
    total_industries = len(industry_counts)
    
    # 분석 결과를 텍스트 파일로 저장
    analysis_file = 'industry_analysis.txt'
    with open(analysis_file, 'w', encoding='utf-8') as f:
        # 헤더 작성
        f.write("="*60 + "\n")
        f.write("업종별 기업 수 분석\n")
        f.write("="*60 + "\n\n")
        f.write(f"총 업종 수: {total_industries}개\n")
        f.write(f"총 기업 수: {total_companies}개\n\n")
        
        # 업종별 기업 수 작성
        f.write("-"*60 + "\n")
        f.write("각 업종별 기업 수:\n")
        f.write("-"*60 + "\n")
        
        for idx, (industry, count) in enumerate(industry_counts.items(), 1):
            f.write(f"{idx:3d}. {industry}: {count}개\n")
        
        # 통계 정보 작성
        f.write("\n" + "="*60 + "\n")
        f.write("통계 요약\n")
        f.write("="*60 + "\n\n")
        f.write(f"평균 기업 수/업종: {industry_counts.mean():.1f}개\n")
        f.write(f"최대 기업 수 업종: {industry_counts.idxmax()} ({industry_counts.max()}개)\n")
        f.write(f"최소 기업 수 업종: {industry_counts.idxmin()} ({industry_counts.min()}개)\n")
        
        # 상위 10개 업종 정보
        f.write("\n" + "-"*60 + "\n")
        f.write("상위 10개 업종 (기업 수 기준):\n")
        f.write("-"*60 + "\n")
        for idx, (industry, count) in enumerate(industry_counts.head(10).items(), 1):
            percentage = (count / total_companies) * 100
            f.write(f"{idx:2d}. {industry}: {count}개 ({percentage:.1f}%)\n")
        
        f.write("\n" + "="*60 + "\n")
        f.write("분석이 완료되었습니다.\n")
        f.write("="*60 + "\n")
    
    # 터미널에 간단한 요약만 출력
    print("\n" + "="*60)
    print("업종 분석이 완료되었습니다.")
    print("="*60)
    print(f"• 총 업종 수: {total_industries}개")
    print(f"• 총 기업 수: {total_companies}개")
    print(f"• 평균 기업 수/업종: {industry_counts.mean():.1f}개")
    print(f"• 최대 기업 수 업종: {industry_counts.idxmax()} ({industry_counts.max()}개)")
    print(f"• 최소 기업 수 업종: {industry_counts.idxmin()} ({industry_counts.min()}개)")
    print("\n상세한 분석 결과는 'industry_analysis.txt' 파일을 확인해주세요.")
    print("="*60)

if __name__ == '__main__':
    EXCEL_FILE = '상장법인목록.xlsx'
    JSON_OUTPUT_FILE = 'companies.json'
    process_company_data(EXCEL_FILE, JSON_OUTPUT_FILE)
