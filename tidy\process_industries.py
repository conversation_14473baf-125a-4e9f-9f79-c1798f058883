import json
import os
from collections import defaultdict

def load_json_file(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_file(data, filepath):
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def main():
    # Define file paths
    base_dir = os.path.dirname(os.path.abspath(__file__))
    companies_path = os.path.join(os.path.dirname(base_dir), 'companies.json')
    mapping_path = os.path.join(os.path.dirname(base_dir), 'industry_mapping.json')
    output_dir = os.path.join(base_dir, 'tidy')
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the data
    companies = load_json_file(companies_path)
    industry_mapping = load_json_file(mapping_path)
    
    # Get the list of industries to remove (from the 64th category onwards with < 4 companies)
    industry_counts = defaultdict(int)
    for company in companies:
        industry = company['industry']
        industry_counts[industry] += 1
    
    # Get the list of all industries in order from industry_analysis.txt
    # We'll use the industry_mapping keys as they seem to be in order
    all_industries = list(industry_mapping.keys())
    
    # Find industries to remove (from 64th onwards with < 4 companies)
    industries_to_remove = []
    for i, industry in enumerate(all_industries[63:], 64):  # Start from 64th industry (0-based index 63)
        if industry_counts.get(industry, 0) < 4:
            industries_to_remove.append(industry)
    
    print(f"Found {len(industries_to_remove)} industries to remove.")
    print("Industries to be removed:")
    for industry in industries_to_remove:
        print(f"- {industry} ({industry_counts.get(industry, 0)} companies)")
    
    # Remove companies that belong to these industries
    filtered_companies = [
        company for company in companies 
        if company['industry'] not in industries_to_remove
    ]
    
    # Remove these industries from the industry mapping
    filtered_mapping = {
        k: v for k, v in industry_mapping.items()
        if k not in industries_to_remove and v not in industries_to_remove
    }
    
    # Save the filtered data
    output_companies_path = os.path.join(output_dir, 'companies.json')
    output_mapping_path = os.path.join(output_dir, 'industry_mapping.json')
    
    save_json_file(filtered_companies, output_companies_path)
    save_json_file(filtered_mapping, output_mapping_path)
    
    print(f"\nOriginal number of companies: {len(companies)}")
    print(f"Number of companies after filtering: {len(filtered_companies)}")
    print(f"Original number of industries in mapping: {len(industry_mapping)}")
    print(f"Number of industries in mapping after filtering: {len(filtered_mapping)}")
    print("\nFiltered files have been saved to:")
    print(f"- {output_companies_path}")
    print(f"- {output_mapping_path}")

if __name__ == "__main__":
    main()
